/**
 * BASE TOOL INTERFACE FOR VOICEHEALTH AI AGENTS
 * 
 * Provides a standardized interface for agent tools including RAG,
 * medical calculators, drug interaction checkers, and other utilities.
 * 
 * FEATURES:
 * - Type-safe tool interface with capabilities
 * - Performance monitoring and metrics
 * - Error handling and recovery
 * - HIPAA-compliant audit logging
 * - Tool chaining and composition
 */

export type ToolCapability = 
  | 'knowledge_retrieval'
  | 'semantic_search'
  | 'evidence_lookup'
  | 'guideline_access'
  | 'research_query'
  | 'drug_interaction'
  | 'medical_calculation'
  | 'risk_assessment'
  | 'diagnostic_support'
  | 'treatment_planning';

export interface ToolRequest {
  query: string;
  parameters?: Record<string, any>;
  capabilities?: ToolCapability[];
  sessionId: string;
  agentId: string;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

export interface ToolResponse {
  toolId: string;
  toolName: string;
  success: boolean;
  result?: any;
  error?: string;
  confidence: number; // 0-1 scale
  executionTime: number; // milliseconds
  metadata?: Record<string, any>;
  sources?: string[];
  citations?: ToolCitation[];
}

export interface ToolCitation {
  source: string;
  title: string;
  url?: string;
  evidenceLevel?: 'A' | 'B' | 'C' | 'D';
  relevanceScore: number;
  excerpt?: string;
}

export interface ToolPerformanceMetrics {
  totalExecutions: number;
  averageExecutionTime: number;
  successRate: number;
  averageConfidence: number;
  lastExecution: string;
  errorCount: number;
  mostCommonErrors: string[];
}

/**
 * Base Tool Interface
 * All agent tools must implement this interface
 */
export interface ITool {
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly capabilities: ToolCapability[];
  readonly isActive: boolean;

  // Core methods
  execute(request: ToolRequest): Promise<ToolResponse>;
  canHandle(request: ToolRequest): boolean;
  
  // Lifecycle methods
  initialize(): Promise<void>;
  shutdown(): Promise<void>;
  
  // Health and monitoring
  healthCheck(): Promise<{ healthy: boolean; details: string }>;
  getPerformanceMetrics(): ToolPerformanceMetrics;
}

/**
 * Abstract Base Tool Class
 * Provides common functionality for all tools
 */
export abstract class BaseTool implements ITool {
  public readonly id: string;
  public readonly name: string;
  public readonly description: string;
  public readonly capabilities: ToolCapability[];
  public readonly isActive: boolean = true;

  protected performanceMetrics: ToolPerformanceMetrics;

  constructor(
    id: string,
    name: string,
    description: string,
    capabilities: ToolCapability[]
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.capabilities = capabilities;
    
    this.performanceMetrics = {
      totalExecutions: 0,
      averageExecutionTime: 0,
      successRate: 0,
      averageConfidence: 0,
      lastExecution: new Date().toISOString(),
      errorCount: 0,
      mostCommonErrors: []
    };
  }

  /**
   * Abstract method - must be implemented by each tool
   */
  abstract execute(request: ToolRequest): Promise<ToolResponse>;

  /**
   * Check if this tool can handle the request
   * Default implementation checks capabilities
   */
  canHandle(request: ToolRequest): boolean {
    if (!request.capabilities || request.capabilities.length === 0) {
      return false;
    }

    return request.capabilities.some(capability => 
      this.capabilities.includes(capability)
    );
  }

  /**
   * Initialize the tool
   */
  async initialize(): Promise<void> {
    console.log(`✅ Tool initialized: ${this.name}`);
  }

  /**
   * Shutdown the tool
   */
  async shutdown(): Promise<void> {
    console.log(`🛑 Tool shutdown: ${this.name}`);
  }

  /**
   * Health check for the tool
   */
  async healthCheck(): Promise<{ healthy: boolean; details: string }> {
    try {
      // Basic health check - can be overridden by specific tools
      return {
        healthy: this.isActive,
        details: this.isActive ? 'Tool operational' : 'Tool inactive'
      };
    } catch (error) {
      return {
        healthy: false,
        details: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): ToolPerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Update performance metrics after execution
   */
  protected updateMetrics(
    executionTime: number, 
    success: boolean, 
    confidence: number,
    error?: string
  ): void {
    this.performanceMetrics.totalExecutions++;
    
    // Update average execution time
    const totalTime = this.performanceMetrics.averageExecutionTime * (this.performanceMetrics.totalExecutions - 1);
    this.performanceMetrics.averageExecutionTime = (totalTime + executionTime) / this.performanceMetrics.totalExecutions;
    
    // Update success rate
    const totalSuccesses = this.performanceMetrics.successRate * (this.performanceMetrics.totalExecutions - 1);
    this.performanceMetrics.successRate = (totalSuccesses + (success ? 1 : 0)) / this.performanceMetrics.totalExecutions;
    
    // Update average confidence
    if (success) {
      const totalConfidence = this.performanceMetrics.averageConfidence * (this.performanceMetrics.totalExecutions - 1);
      this.performanceMetrics.averageConfidence = (totalConfidence + confidence) / this.performanceMetrics.totalExecutions;
    }
    
    // Update error tracking
    if (!success) {
      this.performanceMetrics.errorCount++;
      if (error) {
        this.performanceMetrics.mostCommonErrors.push(error);
        // Keep only last 10 errors
        if (this.performanceMetrics.mostCommonErrors.length > 10) {
          this.performanceMetrics.mostCommonErrors.shift();
        }
      }
    }
    
    this.performanceMetrics.lastExecution = new Date().toISOString();
  }

  /**
   * Log tool usage for HIPAA compliance
   */
  protected async logToolUsage(
    request: ToolRequest,
    response: ToolResponse,
    additionalContext?: Record<string, any>
  ): Promise<void> {
    try {
      // In a real implementation, this would log to an audit system
      console.log(`🔍 Tool Usage: ${this.name}`, {
        toolId: this.id,
        sessionId: request.sessionId,
        agentId: request.agentId,
        success: response.success,
        executionTime: response.executionTime,
        confidence: response.confidence,
        timestamp: new Date().toISOString(),
        ...additionalContext
      });
    } catch (error) {
      console.error('❌ Failed to log tool usage:', error);
      // Don't throw - logging failure shouldn't break tool execution
    }
  }

  /**
   * Validate tool request
   */
  protected validateRequest(request: ToolRequest): void {
    if (!request.query || request.query.trim().length === 0) {
      throw new Error('Tool request query cannot be empty');
    }

    if (!request.sessionId) {
      throw new Error('Tool request must include sessionId');
    }

    if (!request.agentId) {
      throw new Error('Tool request must include agentId');
    }

    if (request.urgencyLevel && !['low', 'medium', 'high', 'critical'].includes(request.urgencyLevel)) {
      throw new Error('Invalid urgency level');
    }
  }

  /**
   * Create error response
   */
  protected createErrorResponse(
    error: Error | string,
    executionTime: number,
    metadata?: Record<string, any>
  ): ToolResponse {
    const errorMessage = error instanceof Error ? error.message : error;
    
    return {
      toolId: this.id,
      toolName: this.name,
      success: false,
      error: errorMessage,
      confidence: 0,
      executionTime,
      metadata: {
        errorType: 'tool_execution_failed',
        ...metadata
      }
    };
  }

  /**
   * Create success response
   */
  protected createSuccessResponse(
    result: any,
    confidence: number,
    executionTime: number,
    metadata?: Record<string, any> | undefined,
    sources?: string[] | undefined,
    citations?: ToolCitation[] | undefined
  ): ToolResponse {
    return {
      toolId: this.id,
      toolName: this.name,
      success: true,
      result,
      confidence,
      executionTime,
      metadata: metadata || undefined,
      sources: sources || undefined,
      citations: citations || undefined
    };
  }
}

export default BaseTool;
