/**
 * ENHANCEMENT TYPES
 * 
 * TypeScript interfaces and types for the Exceptional Performance
 * Enhancement modules including emotional intelligence, multi-modal
 * diagnostics, goal-oriented conversations, and personalized education.
 */

// Emotional Intelligence Types
export interface EmotionalContext {
  sentiment: 'positive' | 'negative' | 'neutral';
  arousal: number; // 0-1 scale
  valence: number; // -1 to 1 scale
  confidence: number; // 0-1 scale
  emotional_cues: string[];
  intensity: 'low' | 'medium' | 'high';
  stability: number; // 0-1 scale
  timestamp: string;
  session_id: string;
  analysis_duration_ms: number;
}

export interface VocalAnalysisRequest {
  audio_data: Buffer | ArrayBuffer;
  session_id: string;
  user_id: string;
  duration_ms: number;
  language: string;
  emergency_context: boolean;
  format: string;
}

export interface VocalAnalysisResponse {
  success: boolean;
  data?: EmotionalContext;
  error?: string;
  processing_time_ms: number;
  service_provider: string;
  confidence_threshold_met: boolean;
}

export interface CulturalEmotionalContext {
  countryCode: string;
  culturalNorms: string[];
  emotionalExpressionPatterns: Record<string, number>;
  familyInvolvementLevel: 'low' | 'medium' | 'high';
  traditionalMedicineIntegration: boolean;
}

// Visual Analysis Types
export interface VisualAnalysisRequest {
  imageUrl: string;
  imageId: string;
  sessionId: string;
  analysisType: 'general' | 'dermatology' | 'radiology' | 'ophthalmology' | 'emergency';
  clinicalContext?: string;
  patientAge?: number;
  patientGender?: string;
  symptoms?: string[];
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
}

export interface VisualAnalysisResult {
  provider: string;
  analysisType: string;
  confidenceScore: number;
  findings: string[];
  urgencyAssessment: string;
  requiresSpecialistReferral: boolean;
  emergencyFlags: string[];
  technicalQuality: {
    clarity: 'poor' | 'fair' | 'good' | 'excellent';
    lighting: 'poor' | 'adequate' | 'good' | 'optimal';
    focus: 'blurry' | 'soft' | 'sharp' | 'very_sharp';
  };
  recommendations: string[];
  timestamp: string;
  processingTimeMs: number;
}

export interface MedicalImageMetadata {
  id: string;
  sessionId: string;
  fileName: string;
  originalName: string;
  url: string;
  size: number;
  type: string;
  uploadedAt: string;
  analysisStatus: 'pending' | 'analyzing' | 'completed' | 'failed' | 'deleted';
  dbRecord?: any;
}

// Goal Tracking Types
export interface SessionGoal {
  id: string;
  sessionId: string;
  goalType: string;
  primaryGoal: string;
  secondaryGoals: string[];
  priorityLevel: number;
  progressPercentage: number;
  completionCriteria: Record<string, any>;
  steeringNotes: string[];
  createdAt: string;
  updatedAt: string;
}

export interface SteeringGuidance {
  steeringType: 'redirect' | 'clarify' | 'deepen' | 'summarize' | 'conclude' | 'escalate' | 'refer' | 'educate' | 'reassure' | 'investigate';
  steeringMessage: string;
  relevanceScore: number;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  goalId: string;
  turnNumber: number;
  createdAt: string;
}

export interface GoalAnalysis {
  currentGoals: SessionGoal[];
  progressAssessment: Record<string, number>;
  steeringRecommendations: SteeringGuidance[];
  conversationRelevance: number;
  nextActions: string[];
}

export interface ConclusionSignals {
  goalCompletionRate: number;
  conversationLength: number;
  agentConclusionIndicators: string[];
  patientSatisfactionSignals: string[];
  followUpMentioned: boolean;
  emergencyFlagsResolved: boolean;
  timeElapsed: number;
}

export interface ConclusionAnalysis {
  conclusionProbability: number;
  conclusionType: 'natural' | 'goal_complete' | 'time_limit' | 'patient_initiated' | 'emergency_resolved';
  recommendedActions: string[];
  educationalContentNeeded: boolean;
  followUpRequired: boolean;
  satisfactionAssessmentNeeded: boolean;
}

// Educational Content Types
export interface EducationalContent {
  id: string;
  title: string;
  content: string;
  contentType: 'explanation' | 'instructions' | 'prevention' | 'lifestyle' | 'medication' | 'warning_signs';
  targetAudience: 'patient' | 'family' | 'caregiver';
  culturalAdaptations: string[];
  languageCode: string;
  healthLiteracyLevel: 'basic' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  keyTakeaways: string[];
  actionItems: string[];
  resources: Array<{
    type: 'link' | 'phone' | 'location';
    title: string;
    value: string;
    description?: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface EducationRequest {
  sessionId: string;
  userId: string;
  consultationSummary: string;
  diagnosedConditions: string[];
  recommendedTreatments: string[];
  culturalContext: {
    countryCode: string;
    language: string;
    culturalConsiderations: string[];
    familyInvolvementLevel: 'low' | 'medium' | 'high';
    traditionalMedicineAwareness: boolean;
  };
  patientProfile: {
    age: number;
    gender: string;
    educationLevel?: string;
    healthLiteracyLevel: 'basic' | 'intermediate' | 'advanced';
    preferredLanguage: string;
  };
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface EducationalContentFeedback {
  id: string;
  contentId: string;
  userId: string;
  sessionId: string;
  rating: number; // 1-5
  feedback?: string;
  helpful: boolean;
  createdAt: string;
}

// Enhanced Patient Context Types
export interface EnhancedPatientContext {
  uploadedImages: MedicalImageMetadata[];
  hasImages: boolean;
  imageCount: number;
  emotionalContext?: EmotionalContext;
  culturalContext?: CulturalEmotionalContext;
  activeGoals?: SessionGoal[];
  goalProgress?: Record<string, number>;
  educationalContent?: EducationalContent[];
  steeringGuidance?: SteeringGuidance[];
}

// API Response Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    processingTime: number;
    [key: string]: any;
  };
}

export interface EnhancementAPIEndpoints {
  vocalAnalysis: '/api/vocal-analysis';
  visualAnalysis: '/api/visual-analysis';
  goalTracking: '/api/goal-tracking';
  educationalContent: '/api/educational-content';
  medicalImages: '/api/medical-images';
}

// Service Configuration Types
export interface VocalAnalysisConfig {
  provider: 'hume' | 'deepgram' | 'azure' | 'aws' | 'mock';
  apiKey: string;
  confidence_threshold: number;
  rate_limit_per_minute: number;
  emergency_bypass_enabled: boolean;
  cultural_adaptation_enabled: boolean;
}

export interface VisualAnalysisConfig {
  providers: {
    openai: string;
    google: string;
    anthropic: string;
  };
  fallback_chain: string[];
  max_image_size: number;
  supported_formats: string[];
  analysis_timeout_ms: number;
}

export interface GoalTrackingConfig {
  conclusion_threshold: number;
  max_goals_per_session: number;
  steering_guidance_enabled: boolean;
  progress_tracking_interval_ms: number;
}

export interface EducationConfig {
  supported_languages: string[];
  health_literacy_levels: string[];
  cultural_adaptations: Record<string, string[]>;
  content_generation_timeout_ms: number;
  max_content_items_per_session: number;
}

// Error Types
export interface EnhancementError extends Error {
  code: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
  context?: Record<string, any>;
}

export class EmotionalAnalysisError extends Error implements EnhancementError {
  code: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
  context?: Record<string, any> | undefined;

  constructor(
    message: string,
    code: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    retryable: boolean,
    context?: Record<string, any> | undefined
  ) {
    super(message);
    this.name = 'EmotionalAnalysisError';
    this.code = code;
    this.severity = severity;
    this.retryable = retryable;
    this.context = context;
  }
}

export class VisualAnalysisError extends Error implements EnhancementError {
  code: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
  context?: Record<string, any> | undefined;

  constructor(
    message: string,
    code: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    retryable: boolean,
    context?: Record<string, any> | undefined
  ) {
    super(message);
    this.name = 'VisualAnalysisError';
    this.code = code;
    this.severity = severity;
    this.retryable = retryable;
    this.context = context;
  }
}
